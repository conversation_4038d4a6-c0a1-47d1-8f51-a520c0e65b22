package com.birdeye.social.service;
/**
 * <AUTHOR>
 *
 */
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.AiGeneratedPostRecommendedType;
import com.birdeye.social.constant.FilterPostType;
import com.birdeye.social.entities.PostLibMaster;
import com.birdeye.social.model.PostLibFilter;
import com.birdeye.social.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.*;
import java.util.*;
import java.util.stream.Stream;

@Slf4j
public class PostLibSpecification{
    public static Specification<PostLibMaster> getMasterPostLib(PostLibFilter request, List<Integer> taggedPostsIds, Integer enterpriseId,
                                                                Date startDate, Date endDate, List<Integer> socialChannelIds,
                                                                boolean isBusinessEligibleForAIPosts) {

        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (Objects.nonNull(enterpriseId)) {
                predicates.add(criteriaBuilder.equal(root.get("enterpriseId"), enterpriseId));
            }
//            if (CollectionUtils.isNotEmpty(socialChannelIds)) {
//                predicates.add(criteriaBuilder.isTrue(
//                        root.join("postLib").get("sourceId").in(socialChannelIds)
//                ));
//            }

            if (CollectionUtils.isNotEmpty(socialChannelIds)) {
                // Create a predicate for when aiSuggested is false AND sourceId is in socialChannelIds
                Predicate aiSuggestedFalseWithSourceId = criteriaBuilder.and(
                        criteriaBuilder.equal(root.get("aiSuggested"), 0),
                        criteriaBuilder.isTrue(root.join("postLib").get("sourceId").in(socialChannelIds))
                );

                // Create a predicate for when aiSuggested is true (no sourceId check needed)
                Predicate aiSuggestedTrue = criteriaBuilder.equal(root.get("aiSuggested"), 1);

                // Combine with OR - either aiSuggested is true, OR (aiSuggested is false AND sourceId matches)
                predicates.add(criteriaBuilder.or(aiSuggestedTrue, aiSuggestedFalseWithSourceId));
            }

            if (StringUtils.isNotEmpty(request.getSearchText())) {
                String text = request.getSearchText();
                text = text.replace("%", "\\%").replace("_", "\\_").replace("'", "\\'");
                predicates.add(criteriaBuilder.isTrue(
                        criteriaBuilder.like(root.get("postText"), "%" + text + "%")
                ));
            }

            if (Objects.nonNull(startDate)) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createdDate"), startDate));
            }

            if (Objects.nonNull(endDate)) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("createdDate"), endDate));
            }

            if (CollectionUtils.isNotEmpty(request.getCreators())) {
                predicates.add(root.get("createdBy").in(request.getCreators()));
            }

            predicates.add(criteriaBuilder.or(
                    criteriaBuilder.equal(root.get("notRecommended"), AiGeneratedPostRecommendedType.RECOMMENDED.getValue()),
                    criteriaBuilder.isNull(root.get("notRecommended"))
            ));

            Predicate postContentPredicate = getSpecificationForPostContentFilter(request.getPostContent(), criteriaBuilder, root);
            Predicate postSpecificationPredicate = getSpecificationForPostTypeFilter(request.getPostType(), criteriaBuilder, root, isBusinessEligibleForAIPosts);

            if(postContentPredicate != null) {
                predicates.add(postContentPredicate);
            }

            if(postSpecificationPredicate != null) {
                predicates.add(postSpecificationPredicate);
            }


            if (CollectionUtils.isNotEmpty(taggedPostsIds)) {
                predicates.add(root.get("id").in(taggedPostsIds));
            }
            query.distinct(true);

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    private static Predicate getSpecificationForPostContentFilter(List<FilterPostType> postContent,
                                                                                     CriteriaBuilder criteriaBuilder,
                                                                                     Root<PostLibMaster> root) {
        if (CollectionUtils.isEmpty(postContent)) {
            return null;
        }

        // Define criteria for each post content type
        Predicate textOnlyCriteria = criteriaBuilder.and(
                criteriaBuilder.or(root.get("imageIds").isNull(),
                        criteriaBuilder.equal(root.get("imageIds"), "")),
                criteriaBuilder.or(root.get("videoIds").isNull(),
                        criteriaBuilder.equal(root.get("videoIds"), ""))
        );

        Predicate imageOnlyCriteria = criteriaBuilder.and(
                criteriaBuilder.isNotNull(root.get("imageIds")),
                criteriaBuilder.notEqual(root.get("imageIds"), "")
        );

        Predicate videoOnlyCriteria = criteriaBuilder.and(
                criteriaBuilder.isNotNull(root.get("videoIds")),
                criteriaBuilder.notEqual(root.get("videoIds"), "")
        );


        // Build predicate based on selected filters
        return Stream.of(
                        postContent.contains(FilterPostType.TEXT_ONLY) ? textOnlyCriteria : null,
                        postContent.contains(FilterPostType.CONTAINS_PHOTOS) ? imageOnlyCriteria : null,
                        postContent.contains(FilterPostType.CONTAINS_VIDEOS) ? videoOnlyCriteria : null
                )
                .filter(Objects::nonNull) // Filter out null predicates
                .reduce(criteriaBuilder::or)// Combine all predicates with OR
                .orElse(null);
    }

    private static Predicate getSpecificationForPostTypeFilter(List<FilterPostType> postType,
                                                                                 CriteriaBuilder criteriaBuilder,
                                                                                 Root<PostLibMaster> root,
                                                                                boolean isBusinessEligibleForAIPosts) {

        if (CollectionUtils.isEmpty(postType)) {
            return null;
        }

        List<Predicate> typePredicates = new ArrayList<>();

        if (postType.contains(FilterPostType.AI_ASSISTED_POSTS) && !postType.contains(FilterPostType.USER_CREATED_POSTS)) {
            log.info("Adding AI assisted posts filter");
            typePredicates.add(criteriaBuilder.equal(root.get("aiPost"), 1));
        } else if (postType.contains(FilterPostType.USER_CREATED_POSTS) && !postType.contains(FilterPostType.AI_ASSISTED_POSTS)) {
            log.info("Adding User created posts filter");
            typePredicates.add(criteriaBuilder.equal(root.get("aiPost"), 0));
        }

        if (postType.contains(FilterPostType.AI_SUGGESTED_POSTS) && isBusinessEligibleForAIPosts) {
            log.info("Adding AI suggested posts filter");
            typePredicates.add(criteriaBuilder.equal(root.get("aiSuggested"), 1));
        } else if(!isBusinessEligibleForAIPosts) {
            log.info("Business is not eligible for AI posts, skipping AI suggested posts filter");
            typePredicates.add(criteriaBuilder.equal(root.get("aiSuggested"), 0));
        }


        return typePredicates.isEmpty()
                ? null // If no post type filters are selected, return null
                : typePredicates.size() == 1
                ? typePredicates.get(0) // If only one type is selected, return that predicate directly
                : criteriaBuilder.or(typePredicates.toArray(new Predicate[0])); //combine with OR if multiple types are selected
    }
}
