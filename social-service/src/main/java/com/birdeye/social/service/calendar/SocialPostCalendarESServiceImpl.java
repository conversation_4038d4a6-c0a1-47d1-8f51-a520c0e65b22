package com.birdeye.social.service.calendar;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.FilterPostType;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.constant.SocialTagEntityType;
import com.birdeye.social.constant.SocialTagOperation;
import com.birdeye.social.dao.SocialTagRepository;
import com.birdeye.social.dao.reports.SocialAIPostAssetsRepo;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.entities.*;
import com.birdeye.social.entities.report.SocialAIPostAssets;
import com.birdeye.social.insights.constants.ElasticConstants;
import com.birdeye.social.model.*;
import com.birdeye.social.model.approval_workflow.ApprovalEnum;
import com.birdeye.social.model.es.SocialPostCalendarMessage;
import com.birdeye.social.service.*;
import com.birdeye.social.sro.*;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.StringUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.WriteRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.birdeye.social.constant.FilterPostType.*;

@Service
public class SocialPostCalendarESServiceImpl implements SocialPostCalendarESService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SocialPostCalendarESServiceImpl.class);

    private static final String ES_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    private static final Integer MAX_SIZE_OF_ES_RESULTS = 10000;

    @Autowired
    private EsService esService;

    @Autowired
    private SocialTagDBService socialTagDBService;

    @Autowired
    private IBusinessCoreService businessCoreService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ISocialPostsAssetService socialPostsAssetService;

    @Autowired
    private SocialAIPostAssetsRepo socialAIPostAssetsRepo;

    private static final String ES_NAME_GROUPS = "groups";
    private static final String ES_NAME_BUSINESS_LOCATIONS = "business_locations";
    private static final String ES_NAME_BULK_RESELLER_POSTING = "bulk_reseller_testing";
    private static final String GROUPS = "Groups";
    private static final String BUSINESS_LOCATIONS = "Business Locations";
    private static final String BULK_RESELLER_POSTING = "BULK-RESELLER-POSTING";

    @Override
    public List<SocialPostCalendarMessage> searchFromEsIndex(Integer enterpriseId, Date startDate,
                                                             Date endDate, List<Integer> sourceIds,
                                                             List<Integer> publishStateIds, List<Integer> creators, List<Integer> approvals,
                                                             Integer postId, Set<Long> tagIds,
                                                             List<FilterPostType> postType, List<FilterPostType> postContent) {

        BoolQueryBuilder boolQueryBuilder = createEsQueryForEnterpriseId(enterpriseId, startDate, endDate,
                sourceIds, publishStateIds, postId, tagIds, postType, postContent, creators, approvals);
        List<SocialPostCalendarMessage> socialPostEsRequests = new ArrayList<>();
        LOGGER.info("Social post query : {}", boolQueryBuilder);
        SearchRequest searchRequest = new SearchRequest();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.sort("publishTimeStamp", SortOrder.DESC);
        searchSourceBuilder.sort("scheduleInfoId", SortOrder.ASC);
        searchSourceBuilder.size(MAX_SIZE_OF_ES_RESULTS);
        searchSourceBuilder.query(boolQueryBuilder);
        searchRequest.source(searchSourceBuilder);
        searchRequest.indices(ElasticConstants.CALENDAR_DATA.getName());
        try {
            SearchResponse searchResponse = esService.search(searchRequest);
            SearchHit[] searchHits = searchResponse.getHits().getHits();
            for (SearchHit hit : searchHits) {
                SocialPostCalendarMessage socialPostEsRequest = JSONUtils.fromJSON(hit.getSourceAsString(), SocialPostCalendarMessage.class);
                if(Objects.nonNull(socialPostEsRequest))
                    socialPostEsRequest.setPublishDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(socialPostEsRequest.getDatePublish()));
                socialPostEsRequests.add(socialPostEsRequest);
            }
        }catch (IOException exception){
            LOGGER.info("Exception for Elastic search {}",exception.getMessage());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        catch (Exception exception){
            LOGGER.info("Exception for Elastic search {}",exception.getMessage(), exception);
        }
        return socialPostEsRequests;
    }

    @Override
    public List<SocialPostCalendarMessage> searchFromEsIndex(List<Integer> enterpriseIds, Date startDate,
                                                             Date endDate, List<Integer> sourceIds,
                                                             List<Integer> publishStateIds, Set<Long> tagIds) {
        BoolQueryBuilder boolQueryBuilder = createResellerEsQueryForEnterpriseIds(enterpriseIds, startDate, endDate,
                sourceIds, publishStateIds, tagIds);
        List<SocialPostCalendarMessage> socialPostEsRequests = new ArrayList<>();
        LOGGER.info("Reseller V2 Social post query : {}",boolQueryBuilder);
        SearchRequest searchRequest = new SearchRequest();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.sort("scheduleInfoId", SortOrder.ASC);
        searchSourceBuilder.sort("publishTimeStamp", SortOrder.ASC);
        searchSourceBuilder.size(MAX_SIZE_OF_ES_RESULTS);
        searchSourceBuilder.query(boolQueryBuilder);
        searchRequest.source(searchSourceBuilder);
        searchRequest.indices(ElasticConstants.CALENDAR_DATA.getName());
        try {
            SearchResponse searchResponse = esService.search(searchRequest);
            SearchHit[] searchHits = searchResponse.getHits().getHits();
            for (SearchHit hit : searchHits) {
                SocialPostCalendarMessage socialPostEsRequest = JSONUtils.fromJSON(hit.getSourceAsString(), SocialPostCalendarMessage.class);
                if(Objects.nonNull(socialPostEsRequest))
                    socialPostEsRequest.setPublishDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(socialPostEsRequest.getDatePublish()));
                socialPostEsRequests.add(socialPostEsRequest);
            }
        }catch (IOException exception){
            LOGGER.info("Exception for Elastic search {}",exception.getMessage());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        catch (Exception exception){
            LOGGER.info("Exception for Elastic search {}",exception.getMessage(), exception);
        }
        return socialPostEsRequests;
    }

    @Override
    public SocialPostCalendarMessage searchFromEsIndexByAiPostId(Integer aiPostId) {
        LOGGER.info("Search from ES index by AI post id : {}", aiPostId);
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery("aiPostId",aiPostId));
        SearchRequest searchRequest = new SearchRequest();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(MAX_SIZE_OF_ES_RESULTS);
        searchSourceBuilder.query(boolQueryBuilder);
        searchRequest.source(searchSourceBuilder);
        searchRequest.indices(ElasticConstants.CALENDAR_DATA.getName());
        try {
            SearchResponse searchResponse = esService.search(searchRequest);
            SearchHit[] searchHits = searchResponse.getHits().getHits();
            for (SearchHit hit : searchHits) {
                SocialPostCalendarMessage socialPostEsRequest = JSONUtils.fromJSON(hit.getSourceAsString(), SocialPostCalendarMessage.class);
                if(Objects.nonNull(socialPostEsRequest))
                    return socialPostEsRequest;
            }
        }catch (IOException exception){
            LOGGER.info("Exception for Elastic search {}",exception.getMessage());
        } catch (Exception exception){
            LOGGER.info("Exception for Elastic search {}",exception.getMessage(), exception);
        }
        return null;
    }

    @Override
    public List<SocialPostCalendarMessage> getESCalendarSaveObj(SocialPost socialPost, List<SocialPostScheduleInfo> socialPostScheduleInfoList,
                                                                SocialTagEntityMappingRequest socialTagEntityMappingRequest, List<SocialTagBasicDetail> tags,
                                                                List<String> failedPageIds) {
        List<SocialPostCalendarMessage> socialPostCalendarMessageList = new ArrayList<>();
        if(Objects.nonNull(socialPost)
                && !socialPostScheduleInfoList.isEmpty()) {
            SocialPostSchedulerMetadata socialPostSchedulerMetadata = null;
            List<String> postingSites;
            List<String> mediaSequence = Collections.emptyList();
            try {
                SocialPostCalendarMessage socialPostCalendarMessage = null;
                for(SocialPostScheduleInfo socialPostScheduleInfo : socialPostScheduleInfoList) {
                    if (StringUtils.isNotBlank(socialPost.getPostMetadata())) {
                        String postMetaData = socialPost.getPostMetadata();
                        socialPostSchedulerMetadata =
                                JSONUtils.fromJSON(postMetaData, SocialPostSchedulerMetadata.class);
                    }
                    postingSites = getEnabledSocialChannelList(socialPostScheduleInfo);
                    if (StringUtils.isNotEmpty(socialPost.getPostMetadata())) {
                        try {
                            mediaSequence = getMediaSequence(socialPost, socialPostScheduleInfo.getEnterpriseId());
                        } catch (Exception e) {
                            LOGGER.info("Exception occurred while setting media sequence {} ", e.getMessage());
                        }
                    }

                    if (Objects.nonNull(socialTagEntityMappingRequest)
                            && CollectionUtils.isNotEmpty(socialTagEntityMappingRequest.getTagMappings())) {
                        Set<Long> tagIds = new HashSet<>();
                        for (SocialTagMappingOperationRequest tagMapping : socialTagEntityMappingRequest.getTagMappings()) {
                            if (tagMapping.getOperation().equals(SocialTagOperation.CREATE))
                                tagIds = tagMapping.getTagIds();
                        }
                        tags = getSocialTagBasicDetails(socialPostScheduleInfo.getEnterpriseId(), tagIds);
                    }
                    List<MentionData> mentionData = null;
                    if (StringUtils.isNotEmpty(socialPost.getMentions())) {
                        mentionData = JSONUtils.collectionFromJSON(socialPost.getMentions(), MentionData.class);
                    }
                    ApprovalMetadata approvalMetadata = null;
                    if (Objects.nonNull(socialPost.getApprovalMetadata())) {
                        approvalMetadata = JSONUtils.fromJSON(socialPost.getApprovalMetadata(), ApprovalMetadata.class);
                    }
                    String postMethod = null;
                    if(StringUtils.isNotEmpty(socialPostScheduleInfo.getPostMethod())){
                        postMethod = getESPostMethodName(socialPostScheduleInfo.getPostMethod());
                    }
                    socialPostCalendarMessage = SocialPostCalendarMessage
                            .builder()
                            .id(socialPost.getId())
                            .postText(socialPost.getPostText())
                            .aiPost((Objects.isNull(socialPost.getAiPost()) || socialPost.getAiPost() == 0)?false:true)
                            .linkPreviewUrl(socialPost.getLinkPreviewUrl())
                            .postingSites(postingSites)
                            .scheduleInfoId(socialPostScheduleInfo.getId())
                            .datePublish(new SimpleDateFormat(ES_DATE_FORMAT).format(socialPostScheduleInfo.getPublishDate()))
                            .publishTimeStamp(socialPostScheduleInfo.getPublishDate().getTime())
                            .postMetaData(socialPostSchedulerMetadata)
                            .enterpriseId(socialPostScheduleInfo.getEnterpriseId())
                            .sourceId(socialPostScheduleInfo.getSourceId())
                            .createdBy(socialPost.getCreatedBy())
                            .isPublished(socialPostScheduleInfo.getIsPublished())
                            .mediaSequence(mediaSequence)
                            .recordCreatedDate(new SimpleDateFormat(ES_DATE_FORMAT).format(new Date()))
                            .recordUpdatedDate(new SimpleDateFormat(ES_DATE_FORMAT).format(new Date()))
                            .pageIds(socialPostScheduleInfo.getPageIds())
                            .pageIdsNew(socialPostScheduleInfo.getPageIds())
                            .imageIds(StringUtils.isEmpty(socialPost.getImageIds())
                                    ?
                                    null
                                    :
                                    Arrays.stream(socialPost.getImageIds().split(",")).map(Integer::parseInt).collect(Collectors.toList()))
                            .videoIds(StringUtils.isEmpty(socialPost.getVideoIds())
                                    ?
                                    null
                                    :
                                    Arrays.stream(socialPost.getVideoIds().split(",")).map(Integer::parseInt).collect(Collectors.toList()))
                            .compressedImages(StringUtils.isEmpty(socialPost.getCompressedImageIds())
                                    ?
                                    null
                                    :
                                    Arrays.stream(socialPost.getCompressedImageIds().split(",")).map(Integer::parseInt).collect(Collectors.toList()))
                            .tags(tags)
                            .mentions(mentionData)
                            .approvalWorkflowId(socialPost.getApprovalWorkflowId())
                            .approvalStatus(socialPost.getApprovalStatus())
                            .approvalUUId(
                                    Objects.nonNull(approvalMetadata) ? approvalMetadata.getApprovalUUId() : null)
                            .approvalRequestId(
                                    Objects.nonNull(approvalMetadata) ? approvalMetadata.getApprovalRequestId() : null)
                            .referenceStepId(
                                    Objects.nonNull(approvalMetadata) ? approvalMetadata.getReferenceStepId() : null)
                            .conversationId(
                                    Objects.nonNull(approvalMetadata) ? approvalMetadata.getConversationId() : null)
                            .failedPageIds(failedPageIds)
                            .quotedPostIdNew(socialPost.getQuotedPostId())
                            .quotedPostUrl(socialPost.getQuotedPostUrl())
                            .quotedTweetSource(socialPost.getQuotedTweetSource())
                            .postMethod(postMethod)
                            .build();
                    if(Objects.nonNull(socialPostCalendarMessage))
                        socialPostCalendarMessageList.add(socialPostCalendarMessage);

                }
            }catch (Exception ex) {
                LOGGER.info("Exception occurred while settting up data on ES {}", ex.getMessage(), ex);
            }
        } else {
            LOGGER.info("Social Post is null");
        }
        return socialPostCalendarMessageList;
    }

    @Override
    public List<SocialPostCalendarMessage> getCalendarPostSavingObject(SocialPost socialPost, List<SocialPostScheduleInfo> socialPostScheduleInfoList,
                                                            SocialTagEntityMappingRequest socialTagEntityMappingRequest, List<SocialTagBasicDetail> tags,
                                                            List<String> failedPageIds, Integer businessId) {
        List<SocialPostCalendarMessage> socialPostCalendarMessageList = new ArrayList<>();
        if(Objects.nonNull(socialPost)
                && !socialPostScheduleInfoList.isEmpty()) {
            SocialPostSchedulerMetadata socialPostSchedulerMetadata = null;
            List<String> postingSites;
            List<String> mediaSequence = Collections.emptyList();
            try {
                SocialPostCalendarMessage socialPostCalendarMessage = null;
                for(SocialPostScheduleInfo socialPostScheduleInfo : socialPostScheduleInfoList) {
                    if (StringUtils.isNotBlank(socialPost.getPostMetadata())) {
                        String postMetaData = socialPost.getPostMetadata();
                        socialPostSchedulerMetadata =
                                JSONUtils.fromJSON(postMetaData, SocialPostSchedulerMetadata.class);
                    }
                    postingSites = getEnabledSocialChannelList(socialPostScheduleInfo);
                    if (StringUtils.isNotEmpty(socialPost.getPostMetadata())) {
                        try {
                            mediaSequence = getMediaSequence(socialPost, socialPostScheduleInfo.getEnterpriseId());
                        } catch (Exception e) {
                            LOGGER.info("Exception occurred while setting media sequence {} ", e.getMessage());
                        }
                    }
                    tags = setTags(socialTagEntityMappingRequest, tags, businessId);
                    List<MentionData> mentionData = null;
                    if (StringUtils.isNotEmpty(socialPost.getMentions())) {
                        mentionData = JSONUtils.collectionFromJSON(socialPost.getMentions(), MentionData.class);
                    }
                    ApprovalMetadata approvalMetadata = null;
                    if (Objects.nonNull(socialPost.getApprovalMetadata())) {
                        approvalMetadata = JSONUtils.fromJSON(socialPost.getApprovalMetadata(), ApprovalMetadata.class);
                    }
                    String postMethod = null;
                    if(StringUtils.isNotEmpty(socialPostScheduleInfo.getPostMethod())){
                        postMethod = getESPostMethodName(socialPostScheduleInfo.getPostMethod());
                    }
                    socialPostCalendarMessage = SocialPostCalendarMessage
                            .builder()
                            .id(socialPost.getId())
                            .postText(socialPost.getPostText())
                            .linkPreviewUrl(socialPost.getLinkPreviewUrl())
                            .postingSites(postingSites)
                            .scheduleInfoId(socialPostScheduleInfo.getId())
                            .datePublish(new SimpleDateFormat(ES_DATE_FORMAT).format(socialPostScheduleInfo.getPublishDate()))
                            .publishTimeStamp(socialPostScheduleInfo.getPublishDate().getTime())
                            .postMetaData(socialPostSchedulerMetadata)
                            .enterpriseId(socialPostScheduleInfo.getEnterpriseId())
                            .sourceId(socialPostScheduleInfo.getSourceId())
                            .createdBy(socialPost.getCreatedBy())
                            .isPublished(socialPostScheduleInfo.getIsPublished())
                            .mediaSequence(mediaSequence)
                            .recordCreatedDate(new SimpleDateFormat(ES_DATE_FORMAT).format(new Date()))
                            .recordUpdatedDate(new SimpleDateFormat(ES_DATE_FORMAT).format(new Date()))
                            .pageIds(socialPostScheduleInfo.getPageIds())
                            .pageIdsNew(socialPostScheduleInfo.getPageIds())
                            .imageIds(StringUtils.isEmpty(socialPost.getImageIds())
                                    ?
                                    null
                                    :
                                    Arrays.stream(socialPost.getImageIds().split(",")).map(Integer::parseInt).collect(Collectors.toList()))
                            .videoIds(StringUtils.isEmpty(socialPost.getVideoIds())
                                    ?
                                    null
                                    :
                                    Arrays.stream(socialPost.getVideoIds().split(",")).map(Integer::parseInt).collect(Collectors.toList()))
                            .compressedImages(StringUtils.isEmpty(socialPost.getCompressedImageIds())
                                    ?
                                    null
                                    :
                                    Arrays.stream(socialPost.getCompressedImageIds().split(",")).map(Integer::parseInt).collect(Collectors.toList()))
                            .tags(tags)
                            .mentions(mentionData)
                            .approvalWorkflowId(socialPost.getApprovalWorkflowId())
                            .approvalStatus(socialPost.getApprovalStatus())
                            .approvalUUId(
                                    Objects.nonNull(approvalMetadata) ? approvalMetadata.getApprovalUUId() : null)
                            .approvalRequestId(
                                    Objects.nonNull(approvalMetadata) ? approvalMetadata.getApprovalRequestId() : null)
                            .referenceStepId(
                                    Objects.nonNull(approvalMetadata) ? approvalMetadata.getReferenceStepId() : null)
                            .conversationId(
                                    Objects.nonNull(approvalMetadata) ? approvalMetadata.getConversationId() : null)
                            .failedPageIds(failedPageIds)
                            .quotedPostIdNew(socialPost.getQuotedPostId())
                            .quotedPostUrl(socialPost.getQuotedPostUrl())
                            .quotedTweetSource(socialPost.getQuotedTweetSource())
                            .postMethod(postMethod)
                            .build();
                    if(Objects.nonNull(socialPostCalendarMessage))
                        socialPostCalendarMessageList.add(socialPostCalendarMessage);

                }
            }catch (Exception ex) {
                LOGGER.info("Exception occurred while settting up data on ES {}", ex.getMessage(), ex);
            }
        } else {
            LOGGER.info("Social Post is null");
        }
        return socialPostCalendarMessageList;
    }

    private String getESPostMethodName(String postMethod) {
        if(StringUtils.isNotEmpty(postMethod)) {
           if(postMethod.equalsIgnoreCase(GROUPS))
               return ES_NAME_GROUPS;
            else if(postMethod.equalsIgnoreCase(BULK_RESELLER_POSTING))
                return ES_NAME_BULK_RESELLER_POSTING;
            else if(postMethod.equalsIgnoreCase(BUSINESS_LOCATIONS))
                return ES_NAME_BUSINESS_LOCATIONS;
        }
        return null;
    }

    @Override
    public List<SocialPostCalendarMessage> getESCalendarSaveObj(List<SocialPost> socialPosts, List<SocialPostScheduleInfo> socialPostScheduleInfoList,
                                                                SocialTagEntityMappingRequest socialTagEntityMappingRequest) {
        List<SocialPostCalendarMessage> socialPostCalendarMessageList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(socialPosts)
                && CollectionUtils.isNotEmpty(socialPostScheduleInfoList)) {
            SocialPostSchedulerMetadata socialPostSchedulerMetadata = null;
            List<String> postingSites;
            List<String> mediaSequence = Collections.emptyList();
            try {
                SocialPostCalendarMessage socialPostCalendarMessage = null;
                for(SocialPostScheduleInfo socialPostScheduleInfo : socialPostScheduleInfoList) {
                    SocialPost socialPost = null;
                    Optional<SocialPost> socialPostOptional = socialPosts.stream().filter(s -> s.getId().equals(socialPostScheduleInfo.getSocialPostId())).findFirst();
                    if(socialPostOptional.isPresent())
                        socialPost= socialPostOptional.get();
                    if (Objects.nonNull(socialPost)) {
                        if(StringUtils.isNotBlank(socialPost.getPostMetadata())) {
                            String postMetaData  =socialPost.getPostMetadata();
                            socialPostSchedulerMetadata =
                                    JSONUtils.fromJSON(postMetaData, SocialPostSchedulerMetadata.class);
                        }
                        postingSites = getEnabledSocialChannelList(socialPostScheduleInfo);
                        if (StringUtils.isNotEmpty(socialPost.getPostMetadata())) {
                            mediaSequence = getMediaSequence(socialPost, socialPostScheduleInfo.getEnterpriseId());
                        }
                        List<SocialTagBasicDetail> tags = null;
                        if(Objects.nonNull(socialTagEntityMappingRequest)
                                && CollectionUtils.isNotEmpty(socialTagEntityMappingRequest.getTagMappings())) {
                            Set<Long> tagIds = new HashSet<>();
                            for (SocialTagMappingOperationRequest tagMapping : socialTagEntityMappingRequest.getTagMappings()) {
                                if(tagMapping.getOperation().equals(SocialTagOperation.CREATE))
                                    tagIds = tagMapping.getTagIds();
                            }
                            tags = getSocialTagBasicDetails(socialPostScheduleInfo.getEnterpriseId(), tagIds);
                        }
                        List<MentionData> mentionData = null;
                        if(StringUtils.isNotEmpty(socialPost.getMentions())) {
                            mentionData = JSONUtils.collectionFromJSON(socialPost.getMentions(), MentionData.class);
                        }
                        ApprovalMetadata approvalMetadata = null;
                        if (Objects.nonNull(socialPost.getApprovalMetadata())) {
                            approvalMetadata = JSONUtils.fromJSON(socialPost.getApprovalMetadata(), ApprovalMetadata.class);
                        }
                        String postMethod = null;
                        if(StringUtils.isNotEmpty(socialPostScheduleInfo.getPostMethod())){
                            postMethod = getESPostMethodName(socialPostScheduleInfo.getPostMethod());
                        }
                        socialPostCalendarMessage = SocialPostCalendarMessage
                                .builder()
                                .id(socialPost.getId())
                                .postText(socialPost.getPostText())
                                .aiPost((Objects.isNull(socialPost.getAiPost()) || socialPost.getAiPost() == 0)?false:true)
                                .linkPreviewUrl(socialPost.getLinkPreviewUrl())
                                .postingSites(postingSites)
                                .scheduleInfoId(socialPostScheduleInfo.getId())
                                .datePublish(
                                        Objects.nonNull(socialPostScheduleInfo.getPublishDate()) ?
                                                new SimpleDateFormat(ES_DATE_FORMAT).format(socialPostScheduleInfo.getPublishDate())
                                                :
                                                null
                                )
                                .publishTimeStamp(
                                        Objects.nonNull(socialPostScheduleInfo.getPublishDate()) ?
                                                socialPostScheduleInfo.getPublishDate().getTime()
                                                :
                                                null
                                )
                                .postMetaData(socialPostSchedulerMetadata)
                                .enterpriseId(socialPostScheduleInfo.getEnterpriseId())
                                .sourceId(socialPostScheduleInfo.getSourceId())
                                .createdBy(socialPost.getCreatedBy())
                                .isPublished(socialPostScheduleInfo.getIsPublished())
                                .mediaSequence(mediaSequence)
                                .recordCreatedDate(new SimpleDateFormat(ES_DATE_FORMAT).format(new Date()))
                                .recordUpdatedDate(new SimpleDateFormat(ES_DATE_FORMAT).format(new Date()))
                                .pageIds(socialPostScheduleInfo.getPageIds())
                                .pageIdsNew(socialPostScheduleInfo.getPageIds())
                                .imageIds(StringUtils.isEmpty(socialPost.getImageIds())
                                        ?
                                        null
                                        :
                                        Arrays.stream(socialPost.getImageIds().split(",")).map(Integer::parseInt).collect(Collectors.toList()))
                                .videoIds(StringUtils.isEmpty(socialPost.getVideoIds())
                                        ?
                                        null
                                        :
                                        Arrays.stream(socialPost.getVideoIds().split(",")).map(Integer::parseInt).collect(Collectors.toList()))
                                .compressedImages(StringUtils.isEmpty(socialPost.getCompressedImageIds())
                                        ?
                                        null
                                        :
                                        Arrays.stream(socialPost.getCompressedImageIds().split(",")).map(Integer::parseInt).collect(Collectors.toList()))
                                .tags(tags)
                                .mentions(mentionData)
                                .approvalWorkflowId(socialPost.getApprovalWorkflowId())
                                .approvalStatus(socialPost.getApprovalStatus())
                                .approvalUUId(
                                        Objects.nonNull(approvalMetadata) ? approvalMetadata.getApprovalUUId() : null)
                                .approvalRequestId(
                                        Objects.nonNull(approvalMetadata) ? approvalMetadata.getApprovalRequestId() : null)
                                .referenceStepId(
                                        Objects.nonNull(approvalMetadata) ? approvalMetadata.getReferenceStepId() : null)
                                .conversationId(
                                        Objects.nonNull(approvalMetadata) ? approvalMetadata.getConversationId() : null)
//                                .failedPageIds(failedPageIds)
                                .quotedPostIdNew(socialPost.getQuotedPostId())
                                .quotedPostUrl(socialPost.getQuotedPostUrl())
                                .quotedTweetSource(socialPost.getQuotedTweetSource())
                                .postMethod(postMethod)
                                .build();
                        if(Objects.nonNull(socialPostCalendarMessage))
                            socialPostCalendarMessageList.add(socialPostCalendarMessage);
                    }
                }
            }catch (Exception ex) {
                LOGGER.info("Exception occurred while settting up data on ES {}", ex.getMessage(), ex);
            }
        } else {
            LOGGER.info("Social Post is null");
        }
        return socialPostCalendarMessageList;
    }

    private List<SocialTagBasicDetail> getSocialTagBasicDetails(Integer enterpriseId, Set<Long> tagIds) {
        LOGGER.info("Adding tag: {}, to enterpriseId: {}", tagIds, enterpriseId);
        List<SocialTagBasicDetail> tags = new ArrayList<>();
        if(null != tagIds && CollectionUtils.isNotEmpty(tagIds)) {
            List<SocialTagRepository.TagBasicDetails> socialTagMappingInfoList = socialTagDBService.findByTagIdInAndAccountId(tagIds,
                    Math.toIntExact(enterpriseId));
            for (SocialTagRepository.TagBasicDetails socialTagMappingInfo : socialTagMappingInfoList) {
                LOGGER.info("Adding tag: {}, {}", socialTagMappingInfo.getId(), socialTagMappingInfo.getName());
                tags.add(new SocialTagBasicDetail(socialTagMappingInfo.getId(), socialTagMappingInfo.getName()));
            }
        } else {
            LOGGER.info("No tags found");
        }
        return tags;
    }

    @Override
    public void updatePostStatusOnES(Integer socialPostId, List<SocialPostScheduleInfo> socialPostScheduleInfoList) {
        try {
            socialPostScheduleInfoList.forEach(socialPostScheduleInfo -> {
                SocialPostCalendarMessage socialPostEsRequest;
                LOGGER.info("Updating Post ID {} with Status {} on ES", socialPostId, socialPostScheduleInfo.getIsPublished());
                try {
                    String id = getId(socialPostId, socialPostScheduleInfo.getId());
                    socialPostEsRequest = new SocialPostCalendarMessage();
                    socialPostEsRequest.setIsPublished(socialPostScheduleInfo.getIsPublished());
                    socialPostEsRequest.setRecordUpdatedDate(new SimpleDateFormat(ES_DATE_FORMAT).format(new Date()));
                    LOGGER.info("Updating data on ES for id");
                    String doc = JSONUtils.toJSON(socialPostEsRequest, JsonInclude.Include.NON_NULL);
                    esService.updateDocumentWithRefresh(doc, ElasticConstants.CALENDAR_DATA.getName(), id);
                } catch (IOException exception){
                    LOGGER.info("Exception for Elastic search {}",exception.getMessage());
                }
                catch (Exception exception){
                    LOGGER.info("Exception occurred in Elastic search update {}",exception.getMessage(), exception);
                }
            });
        } catch( Exception ex) {
            LOGGER.info("Exception occurred while updating social post status on ES {}", ex.getMessage(), ex);
        }
    }

    public void updatePostStatusOnESInBulk(Integer socialPostId, List<SocialPostScheduleInfo> socialPostScheduleInfoList) {
        try {
            if (CollectionUtils.isEmpty(socialPostScheduleInfoList)) {
                LOGGER.info("No records to update for Post ID {}", socialPostId);
                return;
            }

            BulkRequest bulkRequest = new BulkRequest();
            bulkRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.WAIT_UNTIL);
            String currentDate = new SimpleDateFormat(ES_DATE_FORMAT).format(new Date());

            for (SocialPostScheduleInfo socialPostScheduleInfo : socialPostScheduleInfoList) {
                String id = getId(socialPostId, socialPostScheduleInfo.getId());
                LOGGER.info("Preparing update for Post ID {} with Status {} and socialPost_scheduleInfoId: {} on ES", socialPostId, socialPostScheduleInfo.getIsPublished(), id);

                SocialPostCalendarMessage socialPostEsRequest = new SocialPostCalendarMessage();
                socialPostEsRequest.setIsPublished(socialPostScheduleInfo.getIsPublished());
                socialPostEsRequest.setRecordUpdatedDate(currentDate);

                try {
                    String doc = JSONUtils.toJSON(socialPostEsRequest, JsonInclude.Include.NON_NULL);
                    UpdateRequest updateRequest = new UpdateRequest(ElasticConstants.CALENDAR_DATA.getName(), id)
                            .doc(doc, XContentType.JSON);

                    bulkRequest.add(updateRequest);
                } catch (Exception e) {
                    LOGGER.error("Error serializing document for ID {}: {}", id, e.getMessage(), e);
                }
            }

            if (bulkRequest.numberOfActions() > 0) {
                esService.addBulkDocument(bulkRequest);
            }
        } catch (Exception ex) {
            LOGGER.error("Exception occurred while updating social post status on ES: {}", ex.getMessage(), ex);
        }
    }

    @Override
    public void updatePageIdsOnES(Integer socialPostId, Integer socialPostScheduleInfoId, List<String> pageIds) {

        try {
            String id = getId(socialPostId, socialPostScheduleInfoId);
            SocialPostCalendarMessage socialPostEsRequest = SocialPostCalendarMessage.builder()
                    .pageIds(pageIds)
                    .pageIdsNew(pageIds)
                    .build();
            socialPostEsRequest.setRecordUpdatedDate(new SimpleDateFormat(ES_DATE_FORMAT).format(new Date()));
            LOGGER.info("Updating data on ES for pageIds");
            String doc = JSONUtils.toJSON(socialPostEsRequest, JsonInclude.Include.NON_NULL);
            esService.updateDocumentWithRefresh(doc, ElasticConstants.CALENDAR_DATA.getName(), id);
        } catch (IOException exception){
            LOGGER.info("Exception for Elastic search {}",exception.getMessage());
        }
    }

    private List<String> getEnabledSocialChannelList(SocialPostScheduleInfo socialPostScheduled) {
        List<String> socialChannelEnabled = new ArrayList<>();
        if (socialPostScheduled.getSourceId() == SocialChannel.GOOGLE.getId()) {
            socialChannelEnabled.add("google");
        } else if (socialPostScheduled.getSourceId() == SocialChannel.TWITTER.getId()) {
            socialChannelEnabled.add("twitter");
        } else if (socialPostScheduled.getSourceId() == SocialChannel.FACEBOOK.getId()) {
            socialChannelEnabled.add("facebook");
        } else if (socialPostScheduled.getSourceId() == SocialChannel.INSTAGRAM.getId()) {
            socialChannelEnabled.add("instagram");
        } else if (socialPostScheduled.getSourceId() == SocialChannel.LINKEDIN.getId()) {
            socialChannelEnabled.add("linkedin");
        } else if (socialPostScheduled.getSourceId() == SocialChannel.YOUTUBE.getId()) {
            socialChannelEnabled.add("youtube");
        }else if (socialPostScheduled.getSourceId() == SocialChannel.APPLE_CONNECT.getId()) {
            socialChannelEnabled.add("apple_connect");
        } else if(socialPostScheduled.getSourceId() == SocialChannel.TIKTOK.getId())
            socialChannelEnabled.add("tiktok");
        return socialChannelEnabled;
    }

    private BoolQueryBuilder createEsQueryForEnterpriseId(
            Integer enterpriseId, Date startDate, Date endDate, List<Integer> sourceIds,
            List<Integer> publishStateIds, Integer postId, Set<Long> tagIds,
            List<FilterPostType> postType, List<FilterPostType> postContent, List<Integer> creators,
            List<Integer> approvals) {

        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery("enterpriseId", enterpriseId));
        if(postId != null)
            boolQueryBuilder.must(QueryBuilders.termQuery("id", postId));
        if(Objects.nonNull(startDate) && Objects.nonNull(endDate)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("datePublish")
                    .gte(new SimpleDateFormat(ES_DATE_FORMAT).format(startDate))
                    .lte(new SimpleDateFormat(ES_DATE_FORMAT).format(endDate)));
        }

        if(CollectionUtils.isNotEmpty(creators)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("createdBy", creators));
        }

        if(CollectionUtils.isNotEmpty(approvals)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("approvalWorkflowId", approvals));
        }

        if(!isAiSuggestedPostsOnly(postType)) { //when we have multiple postType filter, apply all filter except AI_SUGGESTED_POSTS
            BoolQueryBuilder shouldQuery = new BoolQueryBuilder();
            buildBaseQuery(shouldQuery, sourceIds, publishStateIds, tagIds, postType, postContent);
            boolQueryBuilder.should(shouldQuery);
        }

        //contains ai suggested filter, apply AI_SUGGESTED_FILTER, along with other filters
        // when there are no for publishState or publishState has all the values we will be needing aiGenerated true
        // when there are no for sourceIds or sourceIds has all the values we will be needing aiGenerated true
        // approvals we do not need to apply AI_SUGGESTED_POSTS (as they ae not eligible in these filters)
        if (shouldIncludeAiSuggestedPosts(postType) &&
                (CollectionUtils.isEmpty(publishStateIds) || (CollectionUtils.isNotEmpty(publishStateIds) && publishStateIds.size() == 5)) &&
                (CollectionUtils.isEmpty(sourceIds) || (CollectionUtils.isNotEmpty(sourceIds) && sourceIds.size() == 8)) &&
                (CollectionUtils.isEmpty(approvals))) {
            boolQueryBuilder.should(buildAiSuggestedPostsQuery(tagIds, postContent));

        }
        boolQueryBuilder.minimumShouldMatch(1);
        return boolQueryBuilder;
    }

    private boolean isAiSuggestedPostsOnly(List<FilterPostType> postType) {
        return CollectionUtils.isNotEmpty(postType) &&
                postType.size() == 1 &&
                postType.contains(FilterPostType.AI_SUGGESTED_POSTS);
    }

    private void buildBaseQuery(BoolQueryBuilder shouldQuery,
                                List<Integer> sourceIds,
                                List<Integer> publishStateIds,
                                Set<Long> tagIds,
                                List<FilterPostType> postType, List<FilterPostType> postContent) {
        if (CollectionUtils.isNotEmpty(sourceIds)) {
            shouldQuery.must(QueryBuilders.termsQuery("sourceId", sourceIds));
        }

        if (CollectionUtils.isNotEmpty(publishStateIds)) {
            shouldQuery.must(QueryBuilders.termsQuery("isPublished", publishStateIds));
        }

        if (CollectionUtils.isNotEmpty(tagIds)) {
            shouldQuery.must(QueryBuilders.termsQuery("tags.id", tagIds));
        }

        buildPostTypeQuery(shouldQuery, postType);
        buildContentTypeQuery(shouldQuery, postContent);
    }

    private void buildPostTypeQuery(BoolQueryBuilder shouldQuery, List<FilterPostType> postType) {
        if (CollectionUtils.isEmpty(postType)) {
            return;
        }

        if (postType.contains(FilterPostType.AI_ASSISTED_POSTS) &&
                !postType.contains(FilterPostType.USER_CREATED_POSTS)) {
            LOGGER.info("Filtering for AI_ASSISTED_POSTS");
            shouldQuery.must(QueryBuilders.termQuery("aiPost", true));
        } else if (postType.contains(FilterPostType.USER_CREATED_POSTS) && !postType.contains(FilterPostType.AI_ASSISTED_POSTS)) {
            LOGGER.info("Filtering for USER_CREATED_POSTS");
            shouldQuery.must(QueryBuilders.boolQuery()
                    .should(QueryBuilders.termQuery("aiPost", false))
                    .should(QueryBuilders.boolQuery()
                            .mustNot(QueryBuilders.existsQuery("aiPost"))));
        }
    }

    private void buildContentTypeQuery(BoolQueryBuilder shouldQuery, List<FilterPostType> postContent) {
        if (CollectionUtils.isEmpty(postContent)) {
            return;
        }

        if (postContent.contains(FilterPostType.TEXT_ONLY)) {
            LOGGER.info("Filtering for TEXT_ONLY");
            shouldQuery.must(QueryBuilders.boolQuery()
                    .mustNot(QueryBuilders.existsQuery("imageIds"))
                    .mustNot(QueryBuilders.existsQuery("videoIds")));
        }

        if (postContent.contains(FilterPostType.CONTAINS_PHOTOS)) {
            LOGGER.info("Filtering for CONTAINS_PHOTOS");
            shouldQuery.must(QueryBuilders.existsQuery("imageIds"));
        }

        if (postContent.contains(FilterPostType.CONTAINS_VIDEOS)) {
            LOGGER.info("Filtering for CONTAINS_VIDEOS");
            shouldQuery.must(QueryBuilders.existsQuery("videoIds"));
        }
    }

    private boolean shouldIncludeAiSuggestedPosts(List<FilterPostType> postType) {
        return CollectionUtils.isEmpty(postType) ||  postType.contains(FilterPostType.AI_SUGGESTED_POSTS);
    }

    private BoolQueryBuilder buildAiSuggestedPostsQuery(Set<Long> tagIds, List<FilterPostType> postContent) {

        BoolQueryBuilder aiQuery = new BoolQueryBuilder()
                .must(QueryBuilders.termQuery("aiGenerated", true));

        if (CollectionUtils.isNotEmpty(tagIds)) {
            aiQuery.must(QueryBuilders.termsQuery("tags.id", tagIds));
        }
        buildContentTypeQuery(aiQuery, postContent);
        LOGGER.info("buildAiSuggestedPostsQuery AI Query {}", aiQuery);
        return aiQuery;
    }



    private BoolQueryBuilder getAIQuery( Set<Long> tagIds, List<FilterPostType> postType) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        if(CollectionUtils.isEmpty(postType) || (CollectionUtils.isNotEmpty(postType) && postType.contains(AI_SUGGESTED_POSTS)))
            boolQueryBuilder.must(QueryBuilders.termQuery("aiGenerated", true));
        if(CollectionUtils.isNotEmpty(tagIds))
            boolQueryBuilder.must(QueryBuilders.termsQuery("tags.id",tagIds));
        LOGGER.info("AI Query {}", boolQueryBuilder);
        return boolQueryBuilder;
    }

    private BoolQueryBuilder createResellerEsQueryForEnterpriseIds(
            List<Integer> enterpriseIds, Date startDate, Date endDate, List<Integer> sourceIds,
            List<Integer> publishStateIds, Set<Long> tagIds ) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termsQuery("enterpriseId",enterpriseIds));
        boolQueryBuilder.must(QueryBuilders.termsQuery("postMethod",Arrays.asList(
                ES_NAME_GROUPS,ES_NAME_BUSINESS_LOCATIONS,ES_NAME_BULK_RESELLER_POSTING)));

        if(Objects.nonNull(startDate) && Objects.nonNull(endDate)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("datePublish")
                    .gte(new SimpleDateFormat(ES_DATE_FORMAT).format(startDate))
                    .lte(new SimpleDateFormat(ES_DATE_FORMAT).format(endDate)));
        }
        if(CollectionUtils.isNotEmpty(sourceIds))
            boolQueryBuilder.must(QueryBuilders.termsQuery("sourceId",sourceIds));
//
        if(CollectionUtils.isNotEmpty(publishStateIds))
            boolQueryBuilder.must(QueryBuilders.termsQuery("isPublished",publishStateIds));
        if(CollectionUtils.isNotEmpty(tagIds))
            boolQueryBuilder.must(QueryBuilders.termsQuery("tags.id",tagIds));
        return boolQueryBuilder;
    }
    @Override
    public void save(List<SocialPostCalendarMessage> socialPostCalendarMessages) {
        LOGGER.info("Saving Events on ES socialPostCalendarMessages {}", socialPostCalendarMessages);
        for(SocialPostCalendarMessage socialPostCalendarMessage : socialPostCalendarMessages) {
            String id = null;
            try {
                // If getScheduleInfoId is null which means this is an AI post so id will be aiPostId
                id = String.valueOf(socialPostCalendarMessage.getAiPostId());
                if (Objects.nonNull(socialPostCalendarMessage.getScheduleInfoId()))
                    id = getId(socialPostCalendarMessage.getId(), socialPostCalendarMessage.getScheduleInfoId());
                LOGGER.info("Creating data on ES for id {}", id);
                String doc = JSONUtils.toJSON(socialPostCalendarMessage, JsonInclude.Include.NON_NULL);
                UpdateRequest request = new UpdateRequest(ElasticConstants.CALENDAR_DATA.getName(),
                        id);
                esService.upsertDocumentWithImmediateRefresh(doc, request);
                LOGGER.info("Created data on ES for id {}", id);
            } catch (Exception e) {
                LOGGER.info("Error while creating calendar_data on ES for id: {} with IO EXCEPTION : {}", id, e.getMessage(), e);
            }
        }

        LOGGER.info("Updated/Inserted Events on ES");
    }

    @Override
    public void updateApprovalRecordOnES(ApprovalMetadata approvalMetadata, List<SocialPostCalendarMessage> socialPostCalendarMessageList,
                                         String status) {

        if (Objects.nonNull(approvalMetadata)
                && !socialPostCalendarMessageList.isEmpty()) {
            try {
                for (SocialPostCalendarMessage socialPostScheduleInfo : socialPostCalendarMessageList) {
                    socialPostScheduleInfo.setApprovalUUId(approvalMetadata.getApprovalUUId());
                    socialPostScheduleInfo.setApprovalStatus(status);
                    socialPostScheduleInfo.setApprovalRequestId(approvalMetadata.getApprovalRequestId());
                    socialPostScheduleInfo.setReferenceStepId(approvalMetadata.getReferenceStepId());
                    // this value is never set, always null
                    socialPostScheduleInfo.setConversationId(approvalMetadata.getConversationId());
                }
                LOGGER.info("Saving approval data on ES");
                save(socialPostCalendarMessageList);
            } catch (Exception ex) {
                LOGGER.info("Exception occurred while settting up data on ES {}", ex.getMessage(), ex);
            }
        } else {
            LOGGER.info("Social Post is null");
        }

    }

    @Override
    public void updateTagsRecordOnES(List<SocialPostCalendarMessage> socialPostInputMessageRequestList, SocialTagEntityMappingRequest socialTagEntityMappingRequest, Map<Long, SocialTag> allExistingTagIdToSocialTagMap) {
        if(Objects.nonNull(socialTagEntityMappingRequest)
                && !socialPostInputMessageRequestList.isEmpty()) {
            List<SocialTagBasicDetail> tags = null;
            try {
                for(SocialPostCalendarMessage socialPostCalendarMessage : socialPostInputMessageRequestList) {
                    Set<Long> tagIds = new HashSet<>();
                    Optional<SocialTagMappingOperationRequest> tagMappingOpt =
                            socialTagEntityMappingRequest.getTagMappings().stream().filter(s -> s.getOperation().equals(SocialTagOperation.CREATE)).findFirst();
                    if(tagMappingOpt.isPresent()) {
                        tagIds = tagMappingOpt.get().getTagIds();
                        if(CollectionUtils.isNotEmpty(tagIds)) {
                            tags = new ArrayList<>();
                            for (Long tagId : tagIds) {
                                SocialTag socialTag = allExistingTagIdToSocialTagMap.get(tagId);
                                if(Objects.nonNull(socialTag)) {
                                    SocialTagBasicDetail socialTagBasicDetail = new SocialTagBasicDetail();
                                    socialTagBasicDetail.setId(tagId);
                                    socialTagBasicDetail.setName(socialTag.getName());
                                    tags.add(socialTagBasicDetail);
                                }
                            }
                            socialPostCalendarMessage.setTags(tags);
                        }
                    }
                }
            }catch (Exception ex) {
                LOGGER.info("Exception occurred while setting up data on ES {}", ex.getMessage(), ex);
            }
        } else {
            LOGGER.info("socialTagEntityMappingRequest is null");
        }
    }
    @Override
    public String getId(Integer socialPostId, Integer scheduleInfoId) {
        return socialPostId +
                "_" +
                scheduleInfoId;
    }

    @Override
    public void deleteRecord(Integer socialPostId, Integer scheduleInfoId){
        LOGGER.info("Deleting record for social post ID  {} and schedule info id {}", socialPostId, scheduleInfoId);
        try {
            if(Objects.nonNull(scheduleInfoId))
                esService.deleteDocument(ElasticConstants.CALENDAR_DATA.getName(), getId(socialPostId, scheduleInfoId));
            else
                esService.deleteDocument(ElasticConstants.CALENDAR_DATA.getName(), String.valueOf(socialPostId));
        } catch (IOException e) {
            LOGGER.info("Exception occurred while deleting record for social post ID  {} and schedule info id {}", socialPostId, scheduleInfoId);
            throw new RuntimeException(e);
        }
    }

    private List<List<String>> splitIntoBatches(List<String> deocIds, int batchSize) {
        List<List<String>> batches = new ArrayList<>();
        for (int i = 0; i < deocIds.size(); i += batchSize) {
            int end = Math.min(i + batchSize, deocIds.size());
            batches.add(deocIds.subList(i, end));
        }
        return batches;
    }

    @Override
    public void deleteRecordInBulk(List<SocialPostScheduleInfo> scheduleInfoList){
        if(CollectionUtils.isNotEmpty(scheduleInfoList)) {
            LOGGER.info("Deleting record for list of size: {}", scheduleInfoList.size());
            List<String> docIds = new ArrayList<>();
            Integer esBulkDeleteSize = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getESBulkDeleteSize();
            scheduleInfoList.forEach(scheduleInfo -> docIds.add(getId(scheduleInfo.getSocialPostId(), scheduleInfo.getId())));
            List<List<String>> batches = splitIntoBatches(docIds, esBulkDeleteSize);
            batches.forEach(batch -> {
                try {
                    esService.deleteDocumentInBulk(ElasticConstants.CALENDAR_DATA.getName(), batch);
                } catch (IOException e) {
                    LOGGER.info("Exception occurred while deleting bulk record for docIds {}", batch, e);
                    throw new RuntimeException(e);
                }
            });
        }
    }

    @Override
    public void deleteRecord(List<String> esIds) {
        esService.deleteDocument(ElasticConstants.CALENDAR_DATA.getName(), esIds);
    }

    @Override
    public void updateTagMappings(Set<Long> toBeOperatedOnTagIds , Map<Long, SocialTag> allExistingTagIdToSocialTagMap,
                                  Long postId, List<Integer> scheduleInfoIds, SocialTagEntityType entityType)  {
        if(CollectionUtils.isNotEmpty(toBeOperatedOnTagIds) && (CollectionUtils.isNotEmpty(scheduleInfoIds) || SocialTagEntityType.AI_POST.equals(entityType))) {
            LOGGER.info("Saving Tags data on ES {} for postId : {} scheduleInfoIds : {}, operableTagIds : {}",allExistingTagIdToSocialTagMap, postId,scheduleInfoIds,toBeOperatedOnTagIds);
            List<SocialPostCalendarMessage> socialPostCalendarMessageList = new ArrayList<>();
            List<SocialTagBasicDetail> socialTagBasicDetailList = new ArrayList<>();
            SocialTagBasicDetail socialTagBasicDetail;

            for(Long tagId : toBeOperatedOnTagIds) {
                LOGGER.info("Saving Tags data on ES for tagId {}",tagId);
                socialTagBasicDetail = new SocialTagBasicDetail();
                SocialTag socialTag = allExistingTagIdToSocialTagMap.get(tagId);
                socialTagBasicDetail.setName(socialTag.getName());
                socialTagBasicDetail.setId(tagId);
                socialTagBasicDetailList.add(socialTagBasicDetail);
            }
            for(Integer scheduleInfoId : scheduleInfoIds) {
                socialPostCalendarMessageList.add(getSocialPostCalendarMessage(postId, scheduleInfoId, socialTagBasicDetailList));
            }
            if(SocialTagEntityType.AI_POST.equals(entityType)) {
                socialPostCalendarMessageList.add(getSocialPostCalendarMessageFoAi(postId, socialTagBasicDetailList));
            }
            save(socialPostCalendarMessageList);
            LOGGER.info("Saved Tags data on ES");
        }
    }

    private SocialPostCalendarMessage getSocialPostCalendarMessage(Long postId, Integer scheduleInfoId, List<SocialTagBasicDetail> socialTagBasicDetailList) {
        return SocialPostCalendarMessage.builder()
                .recordUpdatedDate(getCurrentDateTime())
                .id(Math.toIntExact(postId))
                .scheduleInfoId(scheduleInfoId)
                .tags(socialTagBasicDetailList)
                .build();
    }
    private SocialPostCalendarMessage getSocialPostCalendarMessageFoAi(Long postId, List<SocialTagBasicDetail> socialTagBasicDetailList) {
        return SocialPostCalendarMessage.builder()
                .recordUpdatedDate(getCurrentDateTime())
                .aiPostId(Math.toIntExact(postId))
                .tags(socialTagBasicDetailList)
                .aiGenerated(true)
                .build();
    }

    @Override
    public void updatePostApprovalOnES(SocialPost socialPost, ApprovalMetadata approvalMetadata, ApprovalWorkflowEvent
            approvalWorkflowEvent, List<SocialPostScheduleInfo> socialPostScheduleInfoList) {
        try {
            if(CollectionUtils.isNotEmpty(socialPostScheduleInfoList)
                    && isPostFlag(socialPostScheduleInfoList.get(0).getEnterpriseId())) {
                socialPostScheduleInfoList.forEach(socialPostScheduleInfo -> {
                    SocialPostCalendarMessage socialPostEsRequest;
                    LOGGER.info("Updating approval status on ES for postId {}", socialPost.getId());
                    try {
                        String id = getId(socialPost.getId(), socialPostScheduleInfo.getId());
                        socialPostEsRequest = new SocialPostCalendarMessage();
                        socialPostEsRequest.setApprovalUUId(approvalMetadata.getApprovalUUId());
                        socialPostEsRequest.setReferenceStepId(approvalMetadata.getReferenceStepId());
                        socialPostEsRequest.setApprovalRequestId(approvalMetadata.getApprovalRequestId());
                        socialPostEsRequest.setConversationId(socialPost.getConversationId());
                        socialPostEsRequest.setApprovalStatus(socialPost.getApprovalStatus());
                        if (Objects.nonNull(approvalWorkflowEvent)) {
                            socialPostEsRequest.setApprovalUserIds(approvalWorkflowEvent.getPendingApproverIds());
                            socialPostEsRequest.setApprovalWorkflowId(approvalWorkflowEvent.getApprovalWorkflowId());
                        }
                        socialPostEsRequest.setRecordUpdatedDate(new SimpleDateFormat(ES_DATE_FORMAT).format(new Date()));
                        String doc = JSONUtils.toJSON(socialPostEsRequest, JsonInclude.Include.NON_NULL);
                        esService.updateDocument(doc, ElasticConstants.CALENDAR_DATA.getName(), id);
                    } catch (IOException exception) {
                        LOGGER.info("Exception for Elastic search {}", exception.getMessage());
                    } catch (Exception exception) {
                        LOGGER.info("Exception occurred in Elastic search update {}", exception.getMessage(), exception);
                    }
                });
            }
        } catch (Exception ex) {
            LOGGER.info("Exception occurred while updating social post status on ES");
        }
    }
    public List<SocialPostCalendarMessage> createEsUpdateObject(List<SocialPostScheduleInfo> fetchedScheduleList) {
        List<SocialPostCalendarMessage> socialPostCalendarMessageList= new ArrayList<>();
        for(SocialPostScheduleInfo socialPostScheduleInfo: fetchedScheduleList) {
            socialPostCalendarMessageList.add(
                    SocialPostCalendarMessage
                            .builder()
                            .id(socialPostScheduleInfo.getSocialPostId())
                            .scheduleInfoId(socialPostScheduleInfo.getId())
                            .isPublished(socialPostScheduleInfo.getIsPublished())
                            .sourceId(socialPostScheduleInfo.getSourceId())
                            .build());
        }
        return socialPostCalendarMessageList;
    }

    @Override
    public void updateDocument(List<SocialPostCalendarMessage> socialPostCalendarMessageList) {
        try {
            socialPostCalendarMessageList.forEach(socialPostCalendarMessage -> {
                LOGGER.info("Started Updating edit flow Post IDs {} on ES", socialPostCalendarMessage.getId());
                try {
                    String id = getId(socialPostCalendarMessage.getId(), socialPostCalendarMessage.getScheduleInfoId());
                    socialPostCalendarMessage.setRecordUpdatedDate(new SimpleDateFormat(ES_DATE_FORMAT).format(new Date()));
                    LOGGER.info("Updating edit flow  data on ES for id");
                    String doc = JSONUtils.toJSON(socialPostCalendarMessage, JsonInclude.Include.NON_NULL);
                    esService.updateDocumentWithRefresh(doc, ElasticConstants.CALENDAR_DATA.getName(), id);
                } catch (IOException exception){
                    LOGGER.info("Exception while edit for Elastic search {}",exception.getMessage());
                }
                catch (Exception exception){
                    LOGGER.info("Exception occurred in Elastic search update {}",exception.getMessage(), exception);
                }
            });
        } catch( Exception ex) {
            LOGGER.info("Exception occurred while updating social post status on ES {}", ex.getMessage(), ex);
        }
    }

    @Override
    public List<SocialPostCalendarMessage> findByEsIds(List<SocialPostScheduleInfo> socialPostScheduleInfoList) {
        if(CollectionUtils.isNotEmpty(socialPostScheduleInfoList)) {
            for(SocialPostScheduleInfo r : socialPostScheduleInfoList ) {
                String id = getId(r.getSocialPostId(), r.getId());
                GetResponse doc;
                try {
                    doc = esService.fetchEsDocumentByDocId(id, ElasticConstants.CALENDAR_DATA.getName() );
                    if(doc.isExists()) {
                        SocialPostCalendarMessage socialPostCalendarMessage = JSONUtils.fromJSON(doc.getSourceAsString(), SocialPostCalendarMessage.class);
                        return Collections.singletonList(socialPostCalendarMessage);
                    }
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        return Collections.emptyList();
    }

    @Override
    public boolean isGetFlag(Integer userId) {
        LOGGER.info("Checking GET status for user Id {}", userId);
        String data = CacheManager.getInstance().getCache(SystemPropertiesCache.class)
                .getCalendarRevampGetFlag();
        boolean flag = false;
        if(Objects.nonNull(data) && Objects.nonNull(userId)) {
            if(data.equals("1") || data.contains(userId.toString())) {
                flag = true;
            }
        }
        LOGGER.info("Returning GET flag {} for user id {}", flag, userId);
        return flag;
    }
    @Override
    public boolean isPostFlag(Integer userId) {
        LOGGER.info("Checking Post status for user Id {}", userId);
        String data = CacheManager.getInstance().getCache(SystemPropertiesCache.class)
                .getCalendarRevampPostFlag();
        boolean flag = false;
        if(Objects.nonNull(data) && Objects.nonNull(userId)) {
            if(data.equals("1") || data.contains(userId.toString())) {
                flag = true;
            }
        }
        LOGGER.info("Returning post flag {} for user id {}", flag, userId);
        return flag;

    }

    private String getCurrentDateTime() {
        return new SimpleDateFormat(ES_DATE_FORMAT).format(new Date());
    }


    private List<String> getMediaSequence (SocialPost socialPost, Integer enterpriseId) throws Exception {
        BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(enterpriseId, false);
        List<String> imageUrls =  getMediaUrl(socialPost, businessLiteDTO.getBusinessNumber());
        commonService.sortImageUrlFromSequenceId(imageUrls,socialPost.getPostMetadata());
        return imageUrls;
    }

    private List<String> getMediaUrl(SocialPost socialPost, Long businessNumber) {
        return getMediaUrlsFromImageAndVideoIds(socialPost.getImageIds(), socialPost.getVideoIds(), businessNumber);
    }

    private List<String> getMediaUrlsFromImageAndVideoIds(String commaSeparatedImageIds, String commaSeparatedVideoIds, Long businessNumber) {
        List<String> mediaUrls = new ArrayList<>();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(commaSeparatedImageIds)) {
            Set<Integer> imageIds = Stream.of(commaSeparatedImageIds.split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toSet());
            List<SocialPostsAssets> socialPostsAssets = socialPostsAssetService.findByIds(imageIds);
            socialPostsAssets.forEach(asset -> {
                String imageUrl = asset.getImageUrl(); // Get the image URL from the asset

                // Check if imageUrl is not null and not empty
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(imageUrl)) {
                    String completeImageUrl = socialPostsAssetService.getCompleteImageUrlFromPostAsset(asset, businessNumber.toString());

                    // Check if completeImageUrl is not null before adding to mediaUrls
                    if (Objects.nonNull(completeImageUrl)) {
                        mediaUrls.add(completeImageUrl);
                    }
                }
            });
        }

        if (org.apache.commons.lang3.StringUtils.isNotEmpty(commaSeparatedVideoIds)) {
            Set<Integer> videoIds = Stream.of(commaSeparatedVideoIds.split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toSet());
            List<SocialPostsAssets> socialPostsAssets = socialPostsAssetService.findByIds(videoIds);
            socialPostsAssets.forEach(asset -> {
                String videoUrl = asset.getVideoUrl(); // Get the video URL from the asset

                // Check if videoUrl is not null and not empty
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(videoUrl)) {
                    String completeVideoUrl = socialPostsAssetService.getCompleteVideoUrlFromPostAsset(asset, businessNumber.toString());

                    // Check if completeVideoUrl is not null before adding to mediaUrls
                    if (Objects.nonNull(completeVideoUrl)) {
                        mediaUrls.add(completeVideoUrl);
                    }
                }
            });
        }
        return mediaUrls;
    }

    //for reseller
    @Override
    public List<SocialPostCalendarMessage> getCalendarResellerPostSavingObject(SocialPost socialPost, List<SocialPostScheduleInfo> socialPostScheduleInfoList,
                                                                               SocialTagEntityMappingRequest socialTagEntityMappingRequest, List<SocialTagBasicDetail> tags,
                                                                               List<String> failedPageIds, Integer resellerAccountId) {
        List<SocialPostCalendarMessage> socialPostCalendarMessageList = new ArrayList<>();
        if(Objects.nonNull(socialPost)
                && !socialPostScheduleInfoList.isEmpty()) {
            SocialPostSchedulerMetadata socialPostSchedulerMetadata = null;
            List<String> postingSites;
            List<String> mediaSequence = Collections.emptyList();
            try {
                SocialPostCalendarMessage socialPostCalendarMessage = null;
                for(SocialPostScheduleInfo socialPostScheduleInfo : socialPostScheduleInfoList) {
                    if (StringUtils.isNotBlank(socialPost.getPostMetadata())) {
                        String postMetaData = socialPost.getPostMetadata();
                        socialPostSchedulerMetadata =
                                JSONUtils.fromJSON(postMetaData, SocialPostSchedulerMetadata.class);
                    }
                    postingSites = getEnabledSocialChannelList(socialPostScheduleInfo);
                    mediaSequence = setMediaSequence(socialPost, socialPostScheduleInfo, mediaSequence);
                    tags = setTags(socialTagEntityMappingRequest, tags, resellerAccountId);
                    List<MentionData> mentionData = null;
                    if (StringUtils.isNotEmpty(socialPost.getMentions())) {
                        mentionData = JSONUtils.collectionFromJSON(socialPost.getMentions(), MentionData.class);
                    }
                    ApprovalMetadata approvalMetadata = null;
                    if (Objects.nonNull(socialPost.getApprovalMetadata())) {
                        approvalMetadata = JSONUtils.fromJSON(socialPost.getApprovalMetadata(), ApprovalMetadata.class);
                    }
                    String postMethod = null;
                    if(StringUtils.isNotEmpty(socialPostScheduleInfo.getPostMethod())){
                        postMethod = getESPostMethodName(socialPostScheduleInfo.getPostMethod());
                    }
                    socialPostCalendarMessage = SocialPostCalendarMessage
                            .builder()
                            .id(socialPost.getId())
                            .postText(socialPost.getPostText())
                            .linkPreviewUrl(socialPost.getLinkPreviewUrl())
                            .postingSites(postingSites)
                            .scheduleInfoId(socialPostScheduleInfo.getId())
                            .datePublish(new SimpleDateFormat(ES_DATE_FORMAT).format(socialPostScheduleInfo.getPublishDate()))
                            .publishTimeStamp(socialPostScheduleInfo.getPublishDate().getTime())
                            .postMetaData(socialPostSchedulerMetadata)
                            .enterpriseId(socialPostScheduleInfo.getEnterpriseId())
                            .sourceId(socialPostScheduleInfo.getSourceId())
                            .createdBy(socialPost.getCreatedBy())
                            .isPublished(socialPostScheduleInfo.getIsPublished())
                            .mediaSequence(mediaSequence)
                            .recordCreatedDate(new SimpleDateFormat(ES_DATE_FORMAT).format(new Date()))
                            .recordUpdatedDate(new SimpleDateFormat(ES_DATE_FORMAT).format(new Date()))
                            .pageIds(socialPostScheduleInfo.getPageIds())
                            .pageIdsNew(socialPostScheduleInfo.getPageIds())
                            .imageIds(StringUtils.isEmpty(socialPost.getImageIds())
                                    ?
                                    null
                                    :
                                    Arrays.stream(socialPost.getImageIds().split(",")).map(Integer::parseInt).collect(Collectors.toList()))
                            .videoIds(StringUtils.isEmpty(socialPost.getVideoIds())
                                    ?
                                    null
                                    :
                                    Arrays.stream(socialPost.getVideoIds().split(",")).map(Integer::parseInt).collect(Collectors.toList()))
                            .compressedImages(StringUtils.isEmpty(socialPost.getCompressedImageIds())
                                    ?
                                    null
                                    :
                                    Arrays.stream(socialPost.getCompressedImageIds().split(",")).map(Integer::parseInt).collect(Collectors.toList()))
                            .tags(tags)
                            .mentions(mentionData)
                            .approvalWorkflowId(socialPost.getApprovalWorkflowId())
                            .approvalStatus(socialPost.getApprovalStatus())
                            .approvalUUId(
                                    Objects.nonNull(approvalMetadata) ? approvalMetadata.getApprovalUUId() : null)
                            .approvalRequestId(
                                    Objects.nonNull(approvalMetadata) ? approvalMetadata.getApprovalRequestId() : null)
                            .referenceStepId(
                                    Objects.nonNull(approvalMetadata) ? approvalMetadata.getReferenceStepId() : null)
                            .conversationId(socialPost.getConversationId())
                            .failedPageIds(failedPageIds)
                            .quotedPostIdNew(socialPost.getQuotedPostId())
                            .quotedPostUrl(socialPost.getQuotedPostUrl())
                            .quotedTweetSource(socialPost.getQuotedTweetSource())
                            .postMethod(postMethod)
                            .aiPost(Objects.nonNull(socialPost.getAiPost()) ? (socialPost.getAiPost() == 1 ? true : false) : false)
                            .build();
                    if(Objects.nonNull(socialPostCalendarMessage))
                        socialPostCalendarMessageList.add(socialPostCalendarMessage);
                }
            }catch (Exception ex) {
                LOGGER.info("Exception occurred while settting up data on ES {}", ex.getMessage(), ex);
            }
        } else {
            LOGGER.info("Social Post is null");
        }
        return socialPostCalendarMessageList;
    }

    private List<String> setMediaSequence(SocialPost socialPost, SocialPostScheduleInfo socialPostScheduleInfo, List<String> mediaSequence) {
        if (StringUtils.isNotEmpty(socialPost.getPostMetadata())) {
            try {
                mediaSequence = getMediaSequence(socialPost, socialPostScheduleInfo.getEnterpriseId());
            } catch (Exception e) {
                LOGGER.info("Exception occurred while setting media sequence {} ", e.getMessage());
            }
        }
        return mediaSequence;
    }
    private List<SocialTagBasicDetail> setTags(SocialTagEntityMappingRequest socialTagEntityMappingRequest, List<SocialTagBasicDetail> tags, Integer resellerAccountId) {
        if (Objects.nonNull(socialTagEntityMappingRequest)
                && CollectionUtils.isNotEmpty(socialTagEntityMappingRequest.getTagMappings())) {
            Set<Long> tagIds = new HashSet<>();
            for (SocialTagMappingOperationRequest tagMapping : socialTagEntityMappingRequest.getTagMappings()) {
                if (tagMapping.getOperation().equals(SocialTagOperation.CREATE))
                    tagIds = tagMapping.getTagIds();
            }
            if(CollectionUtils.isNotEmpty(tagIds)) {
                tags = new ArrayList<>();
                List<SocialTagRepository.TagBasicDetails> socialTagMappingInfoList = socialTagDBService.findByTagIdInAndAccountId(tagIds,
                        resellerAccountId);
                for (SocialTagRepository.TagBasicDetails socialTagMappingInfo : socialTagMappingInfoList)
                    tags.add(new SocialTagBasicDetail(socialTagMappingInfo.getId(), socialTagMappingInfo.getName()));
            }
        }
        return tags;
    }

    @Override
    public void saveSuspendedDeletedPost(SocialPostEsSyncRequest socialPostsESSyncRequest) throws Exception {
        try {
            LOGGER.info("Posting backup data on ES {}", socialPostsESSyncRequest.getSocialPost().getId());
            String s = JSONUtils.toJSON(socialPostsESSyncRequest,JsonInclude.Include.NON_NULL);
            esService.addDocument(s, ElasticConstants.SUSPENDED_DELETED_POSTS.getName(), String.valueOf(socialPostsESSyncRequest.getPostId()));
            LOGGER.info("Posted backup data on ES {}", socialPostsESSyncRequest.getSocialPost().getId());
        } catch (Exception e) {
            LOGGER.error("IO EXCEPTION WHILE saveSuspendedDeletedPost : {}", e.getMessage(),e);
            throw new Exception(e);
        }
    }
    @Override
    public List<SocialPostEsSyncRequest> getDeletedScheduledPosts(Long enterpriseId) {
        List<SocialPostEsSyncRequest> esSyncRequests = new ArrayList<>();
        try {
            SearchResponse response = esService.search(prepareDeletedScheduledPostEsQuery(enterpriseId));
            SearchHit[] searchHits = response.getHits().getHits();
            for (SearchHit hit : searchHits) {
                esSyncRequests.add(JSONUtils.fromJSON(hit.getSourceAsString(), SocialPostEsSyncRequest.class));
            }
        }
        catch (IOException e) {
            LOGGER.error("IO EXCEPTION WHILE saveSuspendedDeletedPost : {}", e.getMessage(),e);
            throw new RuntimeException(e);
        }
        return esSyncRequests;
    }

    @Override
    public List<SocialPostCalendarMessage> getESCalendarSaveObjForAi(Map<String, PostLibMaster> savedPostMap, Map<String, Long> tagMap) {
        if (savedPostMap.isEmpty()) {
            return Collections.emptyList();
        }

        return savedPostMap.entrySet().stream()
                .map(post -> buildCalendarMessage(post, tagMap))
                .collect(Collectors.toList());
    }

    public SearchRequest prepareDeletedScheduledPostEsQuery(Long enterpriseId) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery(ApprovalEnum.enterpriseId.name(),enterpriseId));
        SearchRequest searchRequest = new SearchRequest();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.size(1000);
        searchRequest.source(searchSourceBuilder);
        searchRequest.indices(ElasticConstants.SUSPENDED_DELETED_POSTS.getName());
        return searchRequest;
    }

    @Override
    public List<SocialPostCalendarMessage> searchFromEsIndexByAiPost(String postId) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery("aiPostId", postId));
        List<SocialPostCalendarMessage> socialPostEsRequests = new ArrayList<>();
        LOGGER.info("AI Reference Post Social post query : {}",boolQueryBuilder);
        SearchRequest searchRequest = new SearchRequest();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(MAX_SIZE_OF_ES_RESULTS);
        searchSourceBuilder.query(boolQueryBuilder);
        searchRequest.source(searchSourceBuilder);
        searchRequest.indices(ElasticConstants.CALENDAR_DATA.getName());
        try {
            SearchResponse searchResponse = esService.search(searchRequest);
            SearchHit[] searchHits = searchResponse.getHits().getHits();
            for (SearchHit hit : searchHits) {
                SocialPostCalendarMessage socialPostEsRequest = JSONUtils.fromJSON(hit.getSourceAsString(), SocialPostCalendarMessage.class);
                if(Objects.nonNull(socialPostEsRequest))
                    socialPostEsRequest.setPublishDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(socialPostEsRequest.getDatePublish()));
                socialPostEsRequests.add(socialPostEsRequest);
            }
        }catch (IOException exception){
            LOGGER.info("Exception for Elastic search {}",exception.getMessage());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        catch (Exception exception){
            LOGGER.info("Exception for Elastic search {}",exception.getMessage(), exception);
        }
        return socialPostEsRequests;
    }

    private SocialPostCalendarMessage buildCalendarMessage(Map.Entry<String, PostLibMaster> idVsPostLibMaster,
                                                           Map<String, Long> tagMap) {
        PostLibMaster post = idVsPostLibMaster.getValue();

        List<Integer> imageIds = getImageIds(post);
        List<Integer> publicCompressedImageIds = getImageIdsFromString(post.getPublicCompressedImageIds());
        List<Integer> publicImageIds = getPublicImageIds(post);
        List<String> imageUrls = getImageUrls(imageIds, publicImageIds);
        List<MediaData> mediaDataList = getMediaDataList(imageUrls);

        LOGGER.info("Building calendar object for AI Post with aiPostId: {}", post.getId());
        return SocialPostCalendarMessage.builder()
                .aiPostId(post.getId()) //post_lib_master id
                .postText(post.getPostText())
                .imageIds(imageIds)
                .mediaSequence(imageUrls)
                .images(mediaDataList)
                .compressedImages(CollectionUtils.isNotEmpty(publicCompressedImageIds)?publicCompressedImageIds:imageIds)
                .compressedMediaSequence(imageUrls)
                .postHeader(post.getPostHeader())
                .datePublish(formatDate(post.getCalendarTime()))
                .publishTimeStamp(getTimestamp(post.getCalendarTime()))
                .aiGenerated(true)
                .aiReferencePostId(post.getPostId()) //externalPostId (postId of page_insight and post_lib_master)
                .aiReason(post.getAiReason())
                .enterpriseId(post.getEnterpriseId())
                .tags(getSocialTagBasicDetails(post.getEnterpriseId(), Collections.singleton(tagMap.get(post.getTag()))))
                .recordCreatedDate(getCurrentDate())
                .recordUpdatedDate(getCurrentDate())
                .build();
    }

    private List<Integer> getImageIds(PostLibMaster post) {
        return Optional.ofNullable(post)
                    .map(PostLibMaster::getImageIds)
                    .filter(Objects::nonNull)
                    .map(ids -> Arrays.stream(ids.split(","))
                                        .map(Integer::valueOf)
                        .collect(Collectors.toList()))
                .orElse(null);
    }

    private List<Integer> getPublicImageIds(PostLibMaster post) {
        return Optional.ofNullable(post)
                .map(PostLibMaster::getPublicImageIds)
                .filter(Objects::nonNull)
                .map(ids -> Arrays.stream(ids.split(","))
                        .map(Integer::valueOf)
                        .collect(Collectors.toList()))
                .orElse(null);
    }
    private List<Integer> getImageIdsFromString(String imageIds) {
        if(StringUtils.isNotEmpty(imageIds)) {
            return Arrays.stream(imageIds.split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
        } else {
            return null;
        }
    }


    private List<String> getImageUrls(List<Integer> imageIds, List<Integer> publicImageIds) {
        List<String> imageUrls = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(imageIds)) {
            List<SocialAIPostAssets> repurposedImages = socialAIPostAssetsRepo.findByAssetIdIn(imageIds);
            if(CollectionUtils.isNotEmpty(repurposedImages)) {
                imageUrls.addAll(repurposedImages.stream().map(SocialAIPostAssets::getImageUrl).collect(Collectors.toList()));
            }
        }

        if(CollectionUtils.isNotEmpty(publicImageIds)) {
            List<SocialAIPostAssets> publicImages = socialAIPostAssetsRepo.findByIdIn(publicImageIds);
            if (CollectionUtils.isNotEmpty(publicImages)) {
                imageUrls.addAll(publicImages.stream().map(SocialAIPostAssets::getImageUrl).collect(Collectors.toList()));
            }
        }
        if(CollectionUtils.isNotEmpty(imageUrls))
            return imageUrls;

        return null;
    }

    private List<MediaData> getMediaDataList(List<String> urls) {
        if (CollectionUtils.isEmpty(urls)) {
            return Collections.emptyList();
        }
        return urls.stream()
                .map(url -> new MediaData(url, null))
                .collect(Collectors.toList());
    }

    private String formatDate(Date date) {
        return new SimpleDateFormat(ES_DATE_FORMAT).format(date);
    }

    private long getTimestamp(Date date) {
        return date.getTime();
    }

    private String getCurrentDate() {
        return formatDate(new Date());
    }
}